import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  NbCardModule,
  NbButtonModule,
  NbCheckboxModule,
  NbSelectModule,
  NbOptionModule,
  NbInputModule,
  NbDialogRef
} from '@nebular/theme';
import { RequirementService } from 'src/services/api/services/requirement.service';
import { GetListRequirementRequest, GetRequirement, GetRequirementListResponseBase } from 'src/services/api/models';

export interface ExtendedRequirementItem extends GetRequirement {
  selected?: boolean;
}

export interface RequestItemImportConfig {
  buildCaseId: number;
  buildCaseName?: string;
  selectedItems: ExtendedRequirementItem[];
  totalItems: number;
  searchCriteria?: {
    CHouseType?: number[];
    CLocation?: string;
    CRequirement?: string;
  };
}

@Component({
  selector: 'app-request-item-import',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NbCardModule,
    NbButtonModule,
    NbCheckboxModule,
    NbSelectModule,
    NbOptionModule,
    NbInputModule
  ],
  templateUrl: './request-item-import.component.html',
  styleUrls: ['./request-item-import.component.scss']
})
export class RequestItemImportComponent implements OnInit {
  @Input() buildCaseId: number = 0;
  @Input() houseType: number[] = [];
  @Output() itemsImported = new EventEmitter<RequestItemImportConfig>();

  currentStep: number = 1;
  requirements: ExtendedRequirementItem[] = [];
  loading: boolean = false;

  // 搜尋相關屬性
  searchRequest: GetListRequirementRequest & { CIsShow?: boolean | null; CIsSimple?: boolean | null } = {};

  constructor(
    private requirementService: RequirementService,
    private dialogRef: NbDialogRef<RequestItemImportComponent>
  ) { }

  ngOnInit() {
    this.initializeSearchForm();
    this.loadRequirementsFromAPI();
  }

  // 初始化搜尋表單
  initializeSearchForm() {
    this.searchRequest.CStatus = 1; // 預設只顯示啟用的項目
    this.searchRequest.CIsShow = null; // 預設只顯示需要顯示的項目
    this.searchRequest.CIsSimple = null;
    this.searchRequest.CRequirement = '';
    this.searchRequest.CLocation = '';
    // 使用外部傳入的參數
    this.searchRequest.CBuildCaseID = this.buildCaseId;
    this.searchRequest.CHouseType = this.houseType;
  }



  // 搜尋事件
  onSearch() {
    this.loadRequirementsFromAPI();
  }

  // 重置搜尋
  resetSearch() {
    this.initializeSearchForm();
    this.loadRequirementsFromAPI();
  }

  loadRequirementsFromAPI() {
    if (!this.searchRequest.CBuildCaseID) {
      return;
    }

    this.loading = true;

    // 準備API請求參數
    const getRequirementListArgs: GetListRequirementRequest = {
      CBuildCaseID: this.searchRequest.CBuildCaseID,
      CHouseType: this.searchRequest.CHouseType,
      CLocation: this.searchRequest.CLocation || null,
      CRequirement: this.searchRequest.CRequirement || null,
      CStatus: this.searchRequest.CStatus,
      CIsShow: this.searchRequest.CIsShow,
      CIsSimple: this.searchRequest.CIsSimple,
      PageIndex: 1,
      PageSize: 100
    };

    this.requirementService.apiRequirementGetRequestListForTemplatePost$Json({
      body: getRequirementListArgs
    }).subscribe({
      next: (response: GetRequirementListResponseBase) => {
        this.loading = false;
        if (response.StatusCode === 0 && response.Entries) {
          this.requirements = response.Entries.map(item => ({
            ...item,
            selected: false
          }));
        } else {
          this.requirements = [];
        }
      },
      error: () => {
        this.loading = false;
        this.requirements = [];
      }
    });
  }

  onRequirementItemChange() {
    // 當需求項目選擇變更時的處理
  }

  getSelectedItems(): ExtendedRequirementItem[] {
    return this.requirements.filter(item => item.selected);
  }

  canProceed(): boolean {
    switch (this.currentStep) {
      case 1:
        return this.getSelectedItems().length > 0;
      case 2:
        return true;
      default:
        return false;
    }
  }

  nextStep() {
    if (this.canProceed() && this.currentStep < 2) {
      this.currentStep++;
    }
  }

  previousStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  getProgressText(): string {
    const progressTexts = {
      1: '請選擇要匯入的需求項目',
      2: '確認匯入詳情'
    };
    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';
  }

  importRequirements() {
    const config: RequestItemImportConfig = {
      buildCaseId: this.searchRequest.CBuildCaseID || 0,
      buildCaseName: '',
      selectedItems: this.getSelectedItems(),
      totalItems: this.getSelectedItems().length,
      searchCriteria: {
        CHouseType: this.searchRequest.CHouseType || undefined,
        CLocation: this.searchRequest.CLocation || undefined,
        CRequirement: this.searchRequest.CRequirement || undefined
      }
    };

    this.itemsImported.emit(config);
    this.close();
  }

  close() {
    this.resetSelections();
    this.dialogRef.close();
  }

  private resetSelections() {
    this.currentStep = 1;
    this.requirements.forEach(requirement => {
      requirement.selected = false;
    });
  }

  selectAll() {
    const allSelected = this.requirements.every(item => item.selected);
    this.requirements.forEach(item => {
      item.selected = !allSelected;
    });
  }

  getSelectedCount(): number {
    return this.getSelectedItems().length;
  }

  getTotalCount(): number {
    return this.requirements.length;
  }
}
